{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@shared/*": ["src/shared/*"], "@components/*": ["src/components/*"], "@navigation/*": ["src/i18n/*"], "@home/*": ["src/modules/home/<USER>"], "@our-services/*": ["src/modules/our-services/*"], "@portfolio/*": ["src/modules/portfolio/*"], "@contacts/*": ["src/modules/contacts/*"], "@about-us/*": ["src/modules/about-us/*"], "tailwindconfig": ["tailwind.config.ts"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}