import React from "react";
import { Project } from "../types/Product";
import Image from "next/image";
import Typography from "@/components/typography";
import DynamicImage from "@/components/dynamic-image";

type Props = {
  project: Project;
};

const ProjectLayout = ({ project }: Props) => {
  return (
    <div className="p-[48px] bg-[#F3F5F8] dark:bg-[#2A2A2A] rounded-[40px]">
      <div className="flex flex-col gap-[50px]">
        <Image src={project.logo} alt="Logo" />

        <Typography variant="h4" className="lg:text-[48px] font-semibold">
          {project.title}
        </Typography>

        <DynamicImage
          imageClassName="w-full h-[704px] object-cover rounded-[30px]"
          url={project.banner}
          alt="Izde Banner"
        />
      </div>
    </div>
  );
};

export default ProjectLayout;
