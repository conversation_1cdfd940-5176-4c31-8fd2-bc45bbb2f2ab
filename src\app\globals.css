@import "tailwindcss";
@config "../../tailwind.config.ts";
@custom-variant dark (&:where(.dark, .dark *));

/* Manrope Font Family */
@font-face {
  font-family: "Manrope";
  src: url("/fonts/manrope/Manrope-ExtraLight.ttf") format("truetype");
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Manrope";
  src: url("/fonts/manrope/Manrope-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Manrope";
  src: url("/fonts/manrope/Manrope-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Manrope";
  src: url("/fonts/manrope/Manrope-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Manrope";
  src: url("/fonts/manrope/Manrope-SemiBold.ttf") format("truetype");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Manrope";
  src: url("/fonts/manrope/Manrope-Bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Manrope";
  src: url("/fonts/manrope/Manrope-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

* {
  font-family: "Manrope", sans-serif;
  scroll-behavior: smooth;
  scroll-margin: 100px;
}

[class="dark"],
[class="dark"] body {
  color: white;
  background: #181818;
}

[class="light"],
[class="light"] body {
  color: #181818;
  background: white;
}

button {
  cursor: pointer;
}

@theme {
  --breakpoint-*: initial;
  --breakpoint-xl: 1440px;
  --breakpoint-lg: 1024px;
  --breakpoint-md: 768px;
  --breakpoint-sm: 420px;
  --breakpoint-xs: 380px;
}

.with-image::after {
  content: "";
  background-image: var(--image-url);
  display: inline-block;
  width: var(--image-width);
  height: var(--image-height);
  background-size: contain;
  background-repeat: no-repeat;
  margin-left: 4px; /* отступ между текстом и изображением */
  vertical-align: middle;
}

@keyframes tiny-bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}
.animate-tiny-bounce {
  animation: tiny-bounce 1s infinite;
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
.animate-blink {
  animation: blink 1s infinite;
}

@keyframes scaleFadeOnce {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* Однократная анимация */
.animate-scale-fade-once {
  animation: scaleFadeOnce 0.6s ease-in-out forwards;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none; /* IE и Edge */
  scrollbar-width: none; /* Firefox */
}
