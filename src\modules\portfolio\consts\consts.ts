import izde_logo from "@portfolio/assets/izde/logo.webp";
import izde_banner from "@portfolio/assets/izde/banner.webp";
import izde_business_logo from "@portfolio/assets/logos/izde_business-logo.svg";
import izde_moderator_logo from "@portfolio/assets/logos/izde_moderator-logo.svg";
import izdesim_logo from "@portfolio/assets/logos/izdesim-logo.svg";
import izdetour_logo from "@portfolio/assets/logos/izdetour-logo.svg";
import { getTranslations } from "next-intl/server";
import { IProduct } from "../types/Product";

const PRODUCTS = async (): Promise<Record<string, any>> => {
  const t = await getTranslations("portfolio");

  return {
    izde: {
      common: {
        title: t("izde.title"),
        description: t("izde.description"),
      },
      projects: [
        {
          id: "izde",
          logo: izde_logo,
          title: t("izde.projects.izde.title"),
          banner: izde_banner,
          aboutProject: {
            title: t("izde.projects.izde.aboutProject.title"),
            sub_title: t("izde.projects.izde.aboutProject.sub_title"),
            offers: t("izde.projects.izde.aboutProject.offers"),
            footer: t("izde.projects.izde.aboutProject.footer"),
          },
        },
      ],
    },
  };
};

export const getProducts = async (): Promise<[string, IProduct][]> => {
  const products = await PRODUCTS();
  return Object.entries(products);
};

export const getProductBySlug = async (
  slug: string
): Promise<IProduct | undefined> => {
  const products = await PRODUCTS();

  return products[slug];
};

// izdesim: {
//   common: null,
//   projects: [],
// },
// izdetour: {
//   common: null,
//   projects: [],
// },
