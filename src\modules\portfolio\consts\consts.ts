import izde_logo from "@portfolio/assets/logos/izde-logo.svg";
import izde_business_logo from "@portfolio/assets/logos/izde_business-logo.svg";
import izde_moderator_logo from "@portfolio/assets/logos/izde_moderator-logo.svg";
import izdesim_logo from "@portfolio/assets/logos/izdesim-logo.svg";
import izdetour_logo from "@portfolio/assets/logos/izdetour-logo.svg";
import { getTranslations } from "next-intl/server";
import { StaticImageData } from "next/image";

interface Product {
  common: Common;
  projects: Project[];
}

interface Project {
  logo: StaticImageData;
  title: string;
  banner: string;
}

interface Common {
  title: string;
  description: string;
}

export const getProducts = async (
  slug?: string
): Promise<Product | undefined | [string, Product][]> => {
  const t = await getTranslations("portfolio");

  const products: Record<string, Product> = {
    izde: {
      common: {
        title: t("izde.title"),
        description: t("izde.description"),
      },
      projects: [
        {
          logo: izde_logo,
          title: t("projects.izde.title"),
          banner:
            "https://nexlink-web-static.s3.eu-central-1.amazonaws.com/nexlink-projects/izde/banner.svg",
        },
      ],
    },
  };

  if (slug) {
    return products[slug];
  }

  return Object.entries(products);
};

// izdesim: {
//   common: null,
//   projects: [],
// },
// izdetour: {
//   common: null,
//   projects: [],
// },
