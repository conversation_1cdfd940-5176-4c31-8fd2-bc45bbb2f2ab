import "./globals.css";
import type { Metadata } from "next";
import { NextIntlClientProvider } from "next-intl";
import { ThemeProvider } from "@/shared/providers/ThemeProvider";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import Providers from "./providers";
import Chat from "@/components/chat";
import { ThemeSetter } from "@/shared/functions/themeSetter";

export const metadata: Metadata = {
  title: "Стандартный заголовок страницы",
};

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;

  return (
    <html lang={locale} className={`light`} suppressHydrationWarning>
      <body className={`max-w-[1920px] m-auto`}>
        <NextIntlClientProvider>
          <Providers>
            <ThemeProvider
              themes={["dark", "light"]}
              attribute="class"
              defaultTheme="light"
              enableSystem={false}
              disableTransitionOnChange
            >
              <ThemeSetter />
              <Navbar />
              {children}
              <Footer />
              <Chat />
            </ThemeProvider>
          </Providers>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
