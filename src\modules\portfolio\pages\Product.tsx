import React from "react";
import Typography from "@/components/typography";
import Image from "next/image";
import { getProducts } from "../consts/consts";

type Props = {
  params: Promise<{ slug: string }>;
};

const Product = async ({ params }: Props) => {
  const { slug } = await params;
  const productDetails = await getProducts(slug);
  console.log(productDetails);
  return (
    <div className="flex flex-col gap-[70px] mt-[110px]">
      {productDetails.common ? (
        <div className="flex flex-col gap-4 max-w-[1220px] m-auto">
          <Typography
            variant="h4"
            className="lg:text-[48px] leading-[56px] font-semibold"
          >
            {productDetails.common.title}
          </Typography>
          <Typography variant="p" className="font-normal">
            {productDetails.common.description}
          </Typography>
        </div>
      ) : null}

      <div className="flex flex-col gap-[88px]">
        <div className="p-[48px] bg-[#F3F5F8] dark:bg-[#2A2A2A] rounded-[40px]">
          <Image
            quality={100}
            loading="eager"
            src={productDetails.projects[0].logo}
            alt="Logo"
          />
        </div>
      </div>
    </div>
  );
};

export default Product;
